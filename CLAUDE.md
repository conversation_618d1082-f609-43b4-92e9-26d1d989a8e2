# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 重要声明

> **项目启动和开发环境配置完全由用户自己管理，Claude 不需要关心启动过程。用户只需按常规方式运行命令即可开始开发。**

## 项目概览

这是一个基于 Next.js 15 + React 19 + TypeScript 的现代化前端开发脚手架，集成 shadcn/ui 组件库，可选 MongoDB 数据库集成，提供完整的 UI 组件解决方案。

### 核心特性

- **零配置启动**：无需数据库即可运行所有功能
- **渐进式增强**：数据库功能按需启用
- **AI优先设计**：专为AI辅助开发优化
- **完整类型安全**：TypeScript严格模式

## 标准开发流程

### 完整四阶段闭环开发

#### 阶段1：需求分析与可行性评估

### 1.1 需求接收与分析

- 仔细分析用户具体需求
- 评估技术可行性和架构兼容性
- 确认需求范围：前端页面/API接口/数据库模型/UI组件

### 1.2 可行性检查清单

```typescript
interface RequirementAnalysis {
  scope: '前端页面' | 'API接口' | '数据库模型' | 'UI组件'
  complexity: '简单' | '中等' | '复杂'
  dependencies: string[]
  breakingChanges: boolean
}
```

### 1.3 架构兼容性验证

- ✅ 检查是否可使用现有 `Repository<T>` 接口
- ✅ 验证是否兼容当前组件库
- ✅ 确认数据库集合需求
- ✅ 评估对现有API的影响

#### 阶段2：需求确认与方案制定

### 2.1 需求确认清单

- [ ] 功能需求的详细描述和预期行为
- [ ] 是否需要数据库支持
- [ ] 页面路由结构确认
- [ ] 权限控制需求
- [ ] 性能要求

### 2.2 技术方案确认

```bash
示例确认话术：
"将为您实现用户管理功能：
- 前端页面：/users 列表页面
- API接口：完整的CRUD接口
- 数据模型：用户表（name, email, role）
- 预计工作量：中等复杂度
请确认是否按此方案开发？"
```

#### 阶段3：开发执行与质量控制

### 3.1 开发执行规范

- 优先使用现有组件和工具函数
- 遵循TypeScript严格模式
- 所有API必须处理数据库未配置情况
- 添加完整错误处理和用户反馈

### 3.2 强制质量检查

每次修改后必须验证：

- [ ] 语法正确性
- [ ] 类型检查通过
- [ ] 编译验证成功
- [ ] 功能测试通过
- [ ] 错误处理完整
- [ ] 用户体验良好

#### 阶段4：测试反馈与交付

### 4.1 完成通知模板

```bash
✅ 功能开发完成！已实现：
- ✅ 用户列表页面：/users
- ✅ CRUD API接口：/api/users
- ✅ 数据验证：Zod schema
- ✅ 错误处理：包含数据库未配置情况

请进行测试：
1. 访问 /users 查看用户列表
2. 测试添加新用户功能
3. 测试编辑和删除功能
```

### 4.2 缺陷处理流程

- [ ] 复现问题场景
- [ ] 分析问题根因
- [ ] 修复并重新测试
- [ ] 更新完成通知

## 技术架构详解

### 核心技术栈

- **Next.js 15** - 支持App Router的全栈React框架
- **React 19** - 最新版本，支持Server Components
- **TypeScript** - 完整类型安全支持
- **Tailwind CSS** - 原子化CSS框架
- **shadcn/ui** - 基于Radix UI的高质量组件库

### 数据层架构

#### 适配器模式

```txt
lib/data/
├── config.ts          // 数据库配置检测
├── index.ts           // 数据适配器工厂
├── types.ts           // 核心类型定义
└── adapters/
    └── mongodb.ts     // MongoDB适配器实现
```

#### 仓储模式使用

```typescript
import { getDataAdapter } from '@/lib/data'

interface User {
  _id?: string
  name: string
  email: string
  avatar?: string
  createdAt?: Date
  updatedAt?: Date
}

const adapter = await getDataAdapter()
if (adapter) {
  const userRepo = adapter.getRepository<User>('users')
  const users = await userRepo.findMany({ active: true })
  const newUser = await userRepo.create({ name: '张三', email: '<EMAIL>' })
}
```

## 项目结构

```txt
src/
├── app/                 # Next.js App Router
│   ├── api/            # API路由
│   ├── globals.css     # 全局样式
│   ├── layout.tsx      # 根布局
│   └── page.tsx        # 首页
├── components/         # 组件
│   ├── ui/            # shadcn/ui组件
│   └── loading.tsx    # 加载组件
├── lib/               # 工具库
│   ├── data/          # 数据层
│   └── utils.ts       # 工具函数
├── types/             # 类型定义
├── constants/         # 应用常量
├── hooks/             # 自定义Hooks
├── public/            # 静态资源
└── styles/            # 样式文件
```

## 实用工具

### 工具函数

```typescript
import { cn, formatDate, debounce, truncate } from '@/lib/utils'

// 类名合并
const className = cn('base-class', condition && 'conditional-class')

// 日期格式化
formatDate(new Date()) // "2024年1月15日"

// 防抖函数
const debouncedSearch = debounce(handleSearch, 300)
```

## 代码示例

### 创建新页面

```typescript
// app/products/page.tsx
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '产品列表',
  description: '查看所有产品信息',
}

export default function ProductsPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">产品列表</h1>
    </div>
  )
}
```

### 创建API路由

```typescript
// app/api/products/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getDataAdapter } from '@/lib/data'

interface Product {
  _id?: string
  name: string
  price: number
  description?: string
}

export async function GET() {
  const data = await getDataAdapter()
  if (!data) {
    return NextResponse.json({ error: '数据库未配置' }, { status: 503 })
  }
  const products = await data.getRepository<Product>('products').findMany()
  return NextResponse.json({ success: true, data: products })
}
```

### 表单组件

```typescript
// components/product-form.tsx
'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button, Input, Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui'

const productSchema = z.object({
  name: z.string().min(1, '产品名称不能为空'),
  price: z.number().min(0, '价格必须大于0'),
})

export function ProductForm() {
  const form = useForm({
    resolver: zodResolver(productSchema),
    defaultValues: { name: '', price: 0 },
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(console.log)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>产品名称</FormLabel>
              <FormControl>
                <Input placeholder="输入产品名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">创建产品</Button>
      </form>
    </Form>
  )
}
```

## 开发约定

### 核心原则

- **用户自主管理**：项目启动、配置、部署完全由用户处理
- **零配置启动**：无需数据库即可运行所有功能
- **类型安全优先**：TypeScript严格模式
- **流程驱动开发**：需求→确认→开发→测试→反馈

### 文件命名规范

- **页面组件**：`page.tsx`
- **API路由**：`route.ts`
- **组件**：PascalCase
- **工具函数**：camelCase

### 安全规范

- 不提交敏感信息
- 使用环境变量存储配置
- 验证所有用户输入
- 实施权限控制

### 质量检查

每次修改后必须验证：

- ✅ 语法正确性
- ✅ 类型检查通过
- ✅ 编译验证成功
- ✅ 功能测试通过
- ✅ 错误处理完整

#### 页面内容处理原则

**重要约定：脚手架初始页面必须修改**

- 如果发现页面是脚手架初始内容（如默认首页、示例页面），**必须完全替换**为用户实际需求的内容
- **不允许保留任何脚手架示例内容**，包括默认文本、图片、示例数据等
- **根路由判断原则**：
  - 如果根路由 `app/page.tsx` 包含脚手架内容（默认Next.js欢迎页等），必须完全替换
  - 如果根路由已包含用户实际功能内容，则保留现有内容
  - 新功能需求应优先评估是否适合根路由，不适合时可创建子路由
- 所有页面内容必须符合用户实际需求，不得保留与需求无关的脚手架元素

这个脚手架已经配置好所有必要工具，开发流程强调需求分析、技术确认、质量控制、测试反馈的完整闭环。
