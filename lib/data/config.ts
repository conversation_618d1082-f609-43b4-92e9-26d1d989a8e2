// 数据适配器配置

export function detectDataAdapter(): 'mongodb' | 'none' {
  const mongoUri = process.env.MONGODB_URI
  
  // 检测 MongoDB URI 是否有效
  if (!mongoUri || mongoUri.trim() === '') {
    return 'none'
  }
  
  // 验证 MongoDB URI 格式
  const mongoPattern = /^mongodb(\+srv)?:\/\/.+/
  if (!mongoPattern.test(mongoUri)) {
    console.warn('Invalid MONGODB_URI format, database features disabled')
    return 'none'
  }
  
  return 'mongodb'
}

export function getMongoUri(): string {
  const uri = process.env.MONGODB_URI
  if (!uri) {
    throw new Error('MONGODB_URI is not configured')
  }
  return uri
}
