// 数据层类型定义

// 通用仓储接口
export interface Repository<T> {
  findMany(filter?: Partial<T>): Promise<T[]>
  findById(id: string): Promise<T | null>
  create(data: Omit<T, '_id' | 'createdAt' | 'updatedAt'>): Promise<T>
  update(id: string, data: Partial<T>): Promise<T | null>
  delete(id: string): Promise<boolean>
}

// 数据适配器接口
export interface DataAdapter {
  getRepository<T>(collectionName: string): Repository<T>
  connect(): Promise<void>
  disconnect(): Promise<void>
}
