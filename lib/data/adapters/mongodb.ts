// MongoDB 数据适配器

import mongoose from 'mongoose'
import type { DataAdapter, Repository } from '../types'
import { getMongoUri } from '../config'

// 通用 Mongoose 仓储实现
class MongooseRepository<T> implements Repository<T> {
  constructor(private collectionName: string) {}

  private getCollection() {
    return mongoose.connection.collection(this.collectionName)
  }

  async findMany(filter: Partial<T> = {}): Promise<T[]> {
    const collection = this.getCollection()
    return await collection.find(filter).toArray() as T[]
  }

  async findById(id: string): Promise<T | null> {
    const collection = this.getCollection()
    const ObjectId = mongoose.Types.ObjectId
    return await collection.findOne({ _id: new ObjectId(id) }) as T | null
  }

  async create(data: Omit<T, '_id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    const collection = this.getCollection()
    const now = new Date()
    const doc = {
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    const result = await collection.insertOne(doc)
    return { ...doc, _id: result.insertedId } as T
  }

  async update(id: string, data: Partial<T>): Promise<T | null> {
    const collection = this.getCollection()
    const ObjectId = mongoose.Types.ObjectId
    const result = await collection.findOneAndUpdate(
      { _id: new ObjectId(id) },
      { ...data, updatedAt: new Date() },
      { returnDocument: 'after' }
    )
    return result as T | null
  }

  async delete(id: string): Promise<boolean> {
    const collection = this.getCollection()
    const ObjectId = mongoose.Types.ObjectId
    const result = await collection.deleteOne({ _id: new ObjectId(id) })
    return result.deletedCount > 0
  }
}

export class MongoAdapter implements DataAdapter {
  private isConnected = false
  private repositories = new Map<string, Repository<any>>()

  getRepository<T>(collectionName: string): Repository<T> {
    if (!this.repositories.has(collectionName)) {
      this.repositories.set(collectionName, new MongooseRepository<T>(collectionName))
    }
    return this.repositories.get(collectionName)!
  }

  async connect(): Promise<void> {
    if (this.isConnected) return

    const uri = getMongoUri()

    await mongoose.connect(uri, {
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 5000,
    })

    this.isConnected = true
    console.log('✅ Connected to MongoDB')
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) return

    await mongoose.disconnect()
    this.isConnected = false
    console.log('📤 Disconnected from MongoDB')
  }
}
