// 数据适配器工厂

import type { DataAdapter } from './types'
import { detectDataAdapter } from './config'
import { MongoAdapter } from './adapters/mongodb'

let dataAdapter: DataAdapter | null = null

export async function getDataAdapter(): Promise<DataAdapter | null> {
  // 如果已经初始化过，直接返回
  if (dataAdapter) return dataAdapter

  const adapterType = detectDataAdapter()

  if (adapterType === 'mongodb') {
    try {
      const mongoAdapter = new MongoAdapter()
      await mongoAdapter.connect()
      dataAdapter = mongoAdapter
      return dataAdapter
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error)
      console.warn('Database features are disabled')
      return null
    }
  }

  // 没有配置数据库
  console.log('📝 No database configured, database features disabled')
  return null
}

// 清理连接（用于应用关闭时）
export async function closeDataAdapter(): Promise<void> {
  if (dataAdapter) {
    await dataAdapter.disconnect()
    dataAdapter = null
  }
}

// 重新导出类型
export type { DataAdapter, Repository } from './types'
