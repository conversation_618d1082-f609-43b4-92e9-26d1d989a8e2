import { NextRequest, NextResponse } from 'next/server'
import { getData<PERSON>dapter } from '@/lib/data'

export async function GET() {
  const data = await getDataAdapter()

  return NextResponse.json({
    success: true,
    data: {
      message: 'Hello from API!',
      database: data ? 'connected' : 'not configured'
    },
  })
}

export async function POST(request: NextRequest) {
  const body = await request.json()

  return NextResponse.json({
    success: true,
    data: { received: body },
  })
}
