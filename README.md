# AI开发脚手架

> 🚀 基于 Next.js 15 + React 19 + TypeScript 的现代化前端开发模板

## ✨ 特性

- 🚀 **Next.js 15** + **React 19** + **TypeScript** - 最新技术栈
- 🎨 **shadcn/ui** + **Tailwind CSS** - 现代化UI组件库
- 📝 **React Hook Form** + **Zod** - 完整的表单解决方案
- 🌈 **next-themes** - 深色/浅色主题切换
- 🗄️ **MongoDB** + **Mongoose** - 可选的数据库集成
- ⚡ **零配置** - 开箱即用的开发环境

## 🚀 快速开始

### 使用模板

1. 点击上方 **"Use this template"** 按钮
2. 创建您的新仓库
3. 克隆到本地开始开发

### 手动克隆

```bash
# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env.local

# 启动开发服务器
pnpm dev
```

## 项目结构

```text
my-project/
├── app/                  # 页面和路由 (Next.js App Router)
│   └── api/             # API 路由
├── components/           # 组件
│   └── ui/              # 基础UI组件 (shadcn/ui)
├── constants/           # 常量定义
├── hooks/               # 自定义Hooks
├── lib/                 # 工具函数
├── types/               # TypeScript 类型定义
└── public/              # 静态资源
```

## 开发指南

### 创建新页面

```typescript
// app/products/page.tsx
export default function ProductsPage() {
  return <div>产品页面</div>
}
```

### 使用UI组件

```typescript
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Loading } from "@/components/loading"

export function MyComponent() {
  return (
    <Card>
      <CardContent>
        <div className="flex items-center gap-4">
          <Button>点击我</Button>
          <Loading text="加载中..." />
        </div>
      </CardContent>
    </Card>
  )
}
```

### 数据库支持（可选）

脚手架支持可选的 MongoDB 数据库集成：

```bash
# .env.local（可选）
MONGODB_URI="mongodb+srv://username:<EMAIL>/database"
```

**特性：**
- 🔄 自动检测：有配置则连接，无配置说明不需要数据库
- 🏗️ 通用架构：支持任意数据模型
- 🎯 类型安全：完整的 TypeScript 支持
- 📚 详细文档：查看 [数据库开发指南](./docs/database-guide.md)

### API 路由示例

```typescript
// app/api/hello/route.ts
import { getDataAdapter } from '@/lib/data'

export async function GET() {
  const data = await getDataAdapter()

  return NextResponse.json({
    message: 'Hello from API!',
    database: data ? 'connected' : 'not configured'
  })
}
```

### 表单处理

```typescript
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

const schema = z.object({
  name: z.string().min(1, "必填"),
})

function MyForm() {
  const form = useForm({
    resolver: zodResolver(schema),
  })

  return (
    <form onSubmit={form.handleSubmit(console.log)}>
      {/* 表单内容 */}
    </form>
  )
}
```

## 可用组件

- **基础**: Button, Input, Card, Badge, Avatar
- **表单**: Form, Select, Checkbox, RadioGroup
- **反馈**: Alert, Progress, Skeleton, Toast
- **导航**: Tabs, Sheet, Dialog

## 📖 文档

- [开发指南](./docs/development-guide.md) - 完整的开发教程和使用说明

## 🚀 部署

```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start
```

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](./CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目基于 [XCoding 私有许可证](./LICENSE)，知识产权归 XCoding 所有。

## ⭐ 支持

如果这个模板对您有帮助，请给个 Star ⭐️

---

**开始构建您的下一个项目吧！** 🚀
