# 数据库开发指南

本脚手架提供了可选的 MongoDB 数据库支持。以下是 AI 开发时的参考指南。

## 🚀 快速开始

### 1. 配置数据库（可选）

```bash
# .env.local
MONGODB_URI="mongodb+srv://username:<EMAIL>/database"
```

**如果不配置 `MONGODB_URI`：**
- 说明当前项目不需要数据库功能
- 应用正常启动，所有非数据库功能都可用
- `getDataAdapter()` 返回 `null`
- 数据库相关的 API 可以返回 503 状态码提示数据库未配置

### 2. 基础使用

```typescript
import { getDataAdapter } from '@/lib/data'

// 获取数据适配器
const data = await getDataAdapter()

if (!data) {
  // 数据库未配置，返回错误或降级处理
  return NextResponse.json(
    { error: 'Database not configured' },
    { status: 503 }
  )
}

// 使用数据库
const repository = data.getRepository<YourType>('collection-name')
```

## 📝 创建数据模型

### 1. 项目结构建议

```text
your-project/
├── types/
│   ├── user.ts          # 用户类型定义
│   ├── post.ts          # 文章类型定义
│   └── index.ts         # 导出所有类型
├── lib/
│   └── data/            # 数据层（脚手架提供）
└── app/api/
    ├── users/           # 用户 API
    └── posts/           # 文章 API
```

### 2. 定义类型接口

```typescript
// types/user.ts
export interface User {
  _id?: string
  name: string
  email?: string
  avatar?: string
  createdAt?: Date
  updatedAt?: Date
}
```

```typescript
// types/index.ts
export type { User } from './user'
export type { Post } from './post'
// ... 导出其他类型
```

### 使用仓储模式

```typescript
// 获取用户仓储
const userRepo = data.getRepository<User>('users')

// CRUD 操作
const users = await userRepo.findMany()
const user = await userRepo.findById(id)
const newUser = await userRepo.create({ name: 'John' })
const updated = await userRepo.update(id, { name: 'Jane' })
const deleted = await userRepo.delete(id)
```

## 🔧 API 路由示例

### 基础 CRUD API

```typescript
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getDataAdapter } from '@/lib/data'

interface User {
  _id?: string
  name: string
  email?: string
}

export async function GET() {
  const data = await getDataAdapter()
  
  if (!data) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 503 }
    )
  }

  const users = await data.getRepository<User>('users').findMany()
  return NextResponse.json({ success: true, data: users })
}

export async function POST(request: NextRequest) {
  const data = await getDataAdapter()
  
  if (!data) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 503 }
    )
  }

  const body = await request.json()
  const user = await data.getRepository<User>('users').create(body)
  return NextResponse.json({ success: true, data: user })
}
```

### 单个资源 API

```typescript
// app/api/users/[id]/route.ts
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const data = await getDataAdapter()
  
  if (!data) {
    return NextResponse.json({ error: 'Database not configured' }, { status: 503 })
  }

  const user = await data.getRepository<User>('users').findById(params.id)
  
  if (!user) {
    return NextResponse.json({ error: 'User not found' }, { status: 404 })
  }

  return NextResponse.json({ success: true, data: user })
}
```

## 💡 最佳实践

### 1. 错误处理

```typescript
try {
  const result = await userRepo.create(userData)
  return NextResponse.json({ success: true, data: result })
} catch (error) {
  console.error('Database error:', error)
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
```

### 2. 数据验证

```typescript
import { z } from 'zod'

const userSchema = z.object({
  name: z.string().min(1),
  email: z.string().email().optional(),
})

export async function POST(request: NextRequest) {
  const body = await request.json()
  
  // 验证数据
  const result = userSchema.safeParse(body)
  if (!result.success) {
    return NextResponse.json(
      { error: 'Invalid data', details: result.error },
      { status: 400 }
    )
  }

  // 创建用户
  const data = await getDataAdapter()
  const user = await data.getRepository<User>('users').create(result.data)
  return NextResponse.json({ success: true, data: user })
}
```

### 3. 查询过滤

```typescript
// 带过滤条件的查询
const activeUsers = await userRepo.findMany({ active: true })
const usersByEmail = await userRepo.findMany({ email: '<EMAIL>' })
```

## 🎯 常见模式

### 分页查询

```typescript
// 注意：当前仓储接口不支持分页，需要扩展
// 这是未来可能的扩展方向
```

### 关联查询

```typescript
// MongoDB 不是关系型数据库，建议使用嵌入文档或手动关联
const user = await userRepo.findById(userId)
const posts = await postRepo.findMany({ authorId: userId })
```

### 索引优化

```typescript
// 在 MongoDB 中为常用查询字段创建索引
// 这需要在数据库层面操作，不在代码中处理
```
